import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { authService } from '../services/authService';
import { User, LoginRequest, RegisterRequest } from '../types/models';

// Auth state interface
interface AuthState {
  isLoading: boolean;
  isAuthenticated: boolean;
  isGuest: boolean;
  user: User | null;
  token: string | null;
  error: string | null;
}

// Auth actions
type AuthAction =
  | { type: 'AUTH_LOADING' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'AUTH_GUEST_MODE'; payload: boolean }
  | { type: 'AUTH_CLEAR_ERROR' }
  | { type: 'AUTH_UPDATE_USER'; payload: User };

// Initial state
const initialState: AuthState = {
  isLoading: false,
  isAuthenticated: false,
  isGuest: false,
  user: null,
  token: null,
  error: null,
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_LOADING':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        isGuest: false,
        user: action.payload.user,
        token: action.payload.token,
        error: null,
      };

    case 'AUTH_ERROR':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        isGuest: false,
        user: null,
        token: null,
        error: action.payload,
      };

    case 'AUTH_LOGOUT':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        isGuest: false,
        user: null,
        token: null,
        error: null,
      };

    case 'AUTH_GUEST_MODE':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        isGuest: action.payload,
        user: null,
        token: null,
        error: null,
      };

    case 'AUTH_CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'AUTH_UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      };

    default:
      return state;
  }
};

// Auth context interface
interface AuthContextType {
  state: AuthState;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  enableGuestMode: () => Promise<void>;
  disableGuestMode: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status on app start
  const checkAuthStatus = useCallback(async () => {
    try {
      // Prevent multiple simultaneous calls
      if (state.isLoading) {
        return;
      }

      dispatch({ type: 'AUTH_LOADING' });

      // Check if user is in guest mode
      const isGuest = await authService.isGuestMode();
      if (isGuest) {
        dispatch({ type: 'AUTH_GUEST_MODE', payload: true });
        return;
      }

      // Check if user is authenticated
      const isAuthenticated = await authService.isAuthenticated();
      if (isAuthenticated) {
        const user = await authService.getStoredUser();
        const token = await authService.getStoredToken();

        if (user && token) {
          console.log('[AuthContext] User authenticated from storage');
          dispatch({
            type: 'AUTH_SUCCESS',
            payload: { user, token }
          });
          return;
        }
      }

      // No valid authentication found, set as guest
      console.log('[AuthContext] No valid auth found, setting as guest');
      dispatch({ type: 'AUTH_LOGOUT' });
    } catch (error) {
      console.error('[Auth] Error checking auth status:', error);
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  }, [state.isLoading]);

  // Login function
  const login = async (credentials: LoginRequest) => {
    try {
      dispatch({ type: 'AUTH_LOADING' });

      const response = await authService.login(credentials);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token
        }
      });
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'Login failed. Please try again.';

      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Register function
  const register = async (userData: RegisterRequest) => {
    try {
      dispatch({ type: 'AUTH_LOADING' });

      const response = await authService.register(userData);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token
        }
      });
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'Registration failed. Please try again.';

      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await authService.logout();
      dispatch({ type: 'AUTH_LOGOUT' });
    } catch (error) {
      console.error('[Auth] Logout error:', error);
      // Force logout even if server request fails
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  // Enable guest mode
  const enableGuestMode = async () => {
    try {
      await authService.enableGuestMode();
      dispatch({ type: 'AUTH_GUEST_MODE', payload: true });
    } catch (error) {
      console.error('[Auth] Error enabling guest mode:', error);
      throw error;
    }
  };

  // Disable guest mode
  const disableGuestMode = async () => {
    try {
      await authService.disableGuestMode();
      dispatch({ type: 'AUTH_LOGOUT' });
    } catch (error) {
      console.error('[Auth] Error disabling guest mode:', error);
      throw error;
    }
  };

  // Update profile
  const updateProfile = async (userData: Partial<User>) => {
    try {
      const updatedUser = await authService.updateProfile(userData);
      dispatch({ type: 'AUTH_UPDATE_USER', payload: updatedUser });
    } catch (error) {
      console.error('[Auth] Error updating profile:', error);
      throw error;
    }
  };

  // Change password
  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      await authService.changePassword(currentPassword, newPassword);
    } catch (error) {
      console.error('[Auth] Error changing password:', error);
      throw error;
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: 'AUTH_CLEAR_ERROR' });
  };

  // Auth status will be checked manually from App.tsx
  // to control the timing and initial navigation

  const contextValue: AuthContextType = {
    state,
    login,
    register,
    logout,
    enableGuestMode,
    disableGuestMode,
    updateProfile,
    changePassword,
    clearError,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
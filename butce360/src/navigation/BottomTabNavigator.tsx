import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../hooks/useThemedStyles';
import { typography } from '../theme/typography';

interface TabBarProps {
  activeTab: string;
  onTabPress: (tab: string) => void;
}

const TabBar: React.FC<TabBarProps> = ({ activeTab, onTabPress }) => {
  const colors = useThemedColors();
  const insets = useSafeAreaInsets();

  const tabs = [
    { id: 'home', icon: 'home-outline', label: '' },
    { id: 'add', icon: 'add-circle', label: '' },
    { id: 'menu', icon: 'menu-outline', label: '' },
  ];

  return (
    <View 
      style={[
        styles.container, 
        { 
          backgroundColor: colors.background.secondary,
          paddingBottom: insets.bottom,
          borderTopColor: colors.border.primary,
        }
      ]}
    >
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        const isAdd = tab.id === 'add';

        return (
          <TouchableOpacity
            key={tab.id}
            style={styles.tab}
            onPress={() => onTabPress(tab.id)}
            activeOpacity={0.7}
          >
            <View style={[
              styles.iconContainer,
              isAdd && [styles.addButton, { backgroundColor: colors.primary[500] }],
              isActive && !isAdd && [styles.activeTab, { backgroundColor: colors.neutral[800] }]
            ]}>
              <Ionicons
                name={tab.icon}
                size={isAdd ? 32 : 28}
                color={
                  isAdd
                    ? colors.background.secondary // add button always white
                    : isActive
                      ? colors.background.secondary // active tab -> white
                      : colors.text.secondary       // inactive tab -> gray
                }
              />
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingTop: 8,
    paddingHorizontal: 16,
    borderTopWidth: 0.33,
    elevation: 8,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  activeTab: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  addButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    elevation: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  tabLabel: {
    ...typography.styles.tabLabel,
    textAlign: 'center',
  },
});

export default TabBar;

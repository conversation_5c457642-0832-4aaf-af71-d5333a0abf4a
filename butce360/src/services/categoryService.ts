import { apiClient } from './api';
import { authService } from './authService';
import { Category, CreateCategoryRequest, UpdateCategoryRequest } from '../types/models';

export class CategoryService {
  private static instance: CategoryService;

  private constructor() {}

  static getInstance(): CategoryService {
    if (!CategoryService.instance) {
      CategoryService.instance = new CategoryService();
    }
    return CategoryService.instance;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await authService.getStoredToken();
    if (!token) {
      throw new Error('No authentication token available');
    }
    return {
      'Authorization': `Bearer ${token}`
    };
  }

  // Get all categories
  async getCategories(type?: 'income' | 'expense'): Promise<Category[]> {
    try {
      const headers = await this.getAuthHeaders();
      const params = type ? `?type=${type}` : '';
      const response = await apiClient.get<any>(`/categories${params}`, headers);

      console.log('[CategoryService] Fetched categories:', response);

      // Transform API response to match frontend format
      const categories = response.data?.data || response.data || [];
      const transformedCategories = Array.isArray(categories) ? categories.map((category: any) => ({
        id: category.id,
        name: category.name,
        type: category.type,
        icon: category.icon,
        color: category.color || this.getDefaultColor(category.type),
        isDefault: category.is_default || false,
        createdAt: category.created_at,
        updatedAt: category.updated_at,
      })) : [];

      return transformedCategories;
    } catch (error) {
      console.error('[CategoryService] Error fetching categories:', error);
      // Return fallback categories if API fails
      return this.getDefaultCategories(type);
    }
  }

  // Get default color for category type
  private getDefaultColor(type: string): string {
    return type === 'income' ? '#22c55e' : '#ef4444';
  }

  // Get category by ID
  async getCategory(id: string): Promise<Category> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<Category>(`/categories/${id}`, headers);

      console.log('[CategoryService] Fetched category:', response.data);
      return response.data;
    } catch (error) {
      console.error('[CategoryService] Error fetching category:', error);
      throw error;
    }
  }

  // Create new category
  async createCategory(categoryData: CreateCategoryRequest): Promise<Category> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.post<Category>('/categories', categoryData, headers);

      console.log('[CategoryService] Created category:', response.data);
      return response.data;
    } catch (error) {
      console.error('[CategoryService] Error creating category:', error);
      throw error;
    }
  }

  // Update category
  async updateCategory(id: string, categoryData: UpdateCategoryRequest): Promise<Category> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await apiClient.put<Category>(`/categories/${id}`, categoryData, headers);

      console.log('[CategoryService] Updated category:', response.data);
      return response.data;
    } catch (error) {
      console.error('[CategoryService] Error updating category:', error);
      throw error;
    }
  }

  // Delete category
  async deleteCategory(id: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      await apiClient.delete(`/categories/${id}`, headers);

      console.log('[CategoryService] Deleted category:', id);
    } catch (error) {
      console.error('[CategoryService] Error deleting category:', error);
      throw error;
    }
  }

  // Get default categories (fallback when API is not available)
  getDefaultCategories(type?: 'income' | 'expense'): Category[] {
    const defaultCategories: Category[] = [
      // Expense categories
      {
        id: 'exp_1',
        name: 'Market',
        type: 'expense',
        icon: '🛒',
        color: '#FF6B6B',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'exp_2',
        name: 'Ulaşım',
        type: 'expense',
        icon: '🚗',
        color: '#4ECDC4',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'exp_3',
        name: 'Yemek',
        type: 'expense',
        icon: '🍽️',
        color: '#45B7D1',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'exp_4',
        name: 'Eğlence',
        type: 'expense',
        icon: '🎬',
        color: '#96CEB4',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'exp_5',
        name: 'Sağlık',
        type: 'expense',
        icon: '🏥',
        color: '#FFEAA7',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      // Income categories
      {
        id: 'inc_1',
        name: 'Maaş',
        type: 'income',
        icon: '💰',
        color: '#00B894',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'inc_2',
        name: 'Freelance',
        type: 'income',
        icon: '💻',
        color: '#6C5CE7',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'inc_3',
        name: 'Yatırım',
        type: 'income',
        icon: '📈',
        color: '#A29BFE',
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    if (type) {
      return defaultCategories.filter(cat => cat.type === type);
    }
    return defaultCategories;
  }
}

// Export singleton instance
export const categoryService = CategoryService.getInstance();

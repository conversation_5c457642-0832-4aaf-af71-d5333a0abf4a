import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

interface BankStatementScreenProps {
  onNavigate?: (screen: string) => void;
}

const BankStatementScreen: React.FC<BankStatementScreenProps> = ({ onNavigate }) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);

  const handleSelectPDF = () => {
    Alert.alert(
      'PDF Seç',
      'Banka ekstrenizi seçin',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Galeri', onPress: () => selectFromGallery() },
        { text: 'Dosyalar', onPress: () => selectFromFiles() },
      ]
    );
  };

  const selectFromGallery = () => {
    // Placeholder for gallery selection
    Alert.alert('Bilgi', 'Galeri seçimi özelliği yakında eklenecek.');
  };

  const selectFromFiles = () => {
    // Placeholder for file selection
    setSelectedFile('sample_bank_statement.pdf');
    Alert.alert('Başarılı', 'PDF dosyası seçildi!');
  };

  const handleProcessPDF = () => {
    if (!selectedFile) {
      Alert.alert('Hata', 'Lütfen önce bir PDF dosyası seçin.');
      return;
    }

    setProcessing(true);
    
    // Simulate PDF processing
    setTimeout(() => {
      setProcessing(false);
      Alert.alert(
        'İşlem Tamamlandı',
        '15 işlem başarıyla içe aktarıldı!',
        [
          { text: 'Tamam', onPress: () => onNavigate?.('Transactions') }
        ]
      );
    }, 3000);
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => onNavigate?.('Menu')}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Banka Ekstresi</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info Section */}
        <View style={styles.section}>
          <View style={styles.infoCard}>
            <View style={styles.infoIconContainer}>
              <Ionicons name="document-text-outline" size={32} color={colors.primary[500]} />
            </View>
            <Text style={styles.infoTitle}>PDF Banka Ekstresi İçe Aktarma</Text>
            <Text style={styles.infoText}>
              Banka ekstrenizi PDF formatında yükleyerek işlemlerinizi otomatik olarak içe aktarabilirsiniz.
            </Text>
          </View>
        </View>

        {/* Supported Banks */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Desteklenen Bankalar</Text>
          <View style={styles.bankList}>
            {[
              'Türkiye İş Bankası',
              'Garanti BBVA',
              'Yapı Kredi',
              'Akbank',
              'Ziraat Bankası',
              'Halkbank',
              'VakıfBank',
              'QNB Finansbank',
            ].map((bank, index) => (
              <View key={index} style={styles.bankItem}>
                <Ionicons name="checkmark-circle-outline" size={20} color={colors.secondary[500]} />
                <Text style={styles.bankName}>{bank}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* File Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>PDF Dosyası</Text>
          
          {!selectedFile ? (
            <TouchableOpacity
              style={styles.selectButton}
              onPress={handleSelectPDF}
            >
              <Ionicons name="cloud-upload-outline" size={32} color={colors.primary[500]} />
              <Text style={styles.selectButtonText}>PDF Dosyası Seç</Text>
              <Text style={styles.selectButtonSubtext}>
                Banka ekstrenizi PDF formatında seçin
              </Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.selectedFile}>
              <View style={styles.fileInfo}>
                <Ionicons name="document-outline" size={24} color={colors.primary[500]} />
                <View style={styles.fileDetails}>
                  <Text style={styles.fileName}>{selectedFile}</Text>
                  <Text style={styles.fileSize}>2.4 MB • PDF</Text>
                </View>
              </View>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={handleRemoveFile}
              >
                <Ionicons name="close-circle-outline" size={24} color={colors.accent[500]} />
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Process Button */}
        {selectedFile && (
          <View style={styles.section}>
            <TouchableOpacity
              style={[
                styles.processButton,
                { backgroundColor: processing ? colors.neutral[400] : colors.primary[500] }
              ]}
              onPress={handleProcessPDF}
              disabled={processing}
            >
              {processing ? (
                <>
                  <Ionicons name="sync-outline" size={20} color={colors.background.secondary} />
                  <Text style={[styles.processButtonText, { color: colors.background.secondary }]}>
                    İşleniyor...
                  </Text>
                </>
              ) : (
                <>
                  <Ionicons name="scan-outline" size={20} color={colors.background.secondary} />
                  <Text style={[styles.processButtonText, { color: colors.background.secondary }]}>
                    PDF'i İşle ve İçe Aktar
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        )}

        {/* Instructions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Nasıl Kullanılır?</Text>
          <View style={styles.instructionsList}>
            <View style={styles.instructionItem}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <Text style={styles.instructionText}>
                Bankanızın mobil uygulaması veya internet bankacılığından PDF ekstre indirin
              </Text>
            </View>
            <View style={styles.instructionItem}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <Text style={styles.instructionText}>
                "PDF Dosyası Seç" butonuna tıklayarak ekstrenizi seçin
              </Text>
            </View>
            <View style={styles.instructionItem}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <Text style={styles.instructionText}>
                "PDF'i İşle ve İçe Aktar" butonuna tıklayarak işlemleri otomatik olarak ekleyin
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.lg,
  },
  infoCard: {
    backgroundColor: colors.background.secondary,
    padding: spacing.xl,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  infoIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  infoTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  infoText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  bankList: {
    gap: spacing.md,
  },
  bankItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.md,
    borderRadius: spacing.sm,
  },
  bankName: {
    ...typography.styles.body2,
    color: colors.text.primary,
    marginLeft: spacing.md,
  },
  selectButton: {
    backgroundColor: colors.background.secondary,
    padding: spacing['2xl'],
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.border.primary,
    borderStyle: 'dashed',
  },
  selectButtonText: {
    ...typography.styles.h6,
    color: colors.text.primary,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  selectButtonSubtext: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  selectedFile: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
  },
  fileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  fileDetails: {
    marginLeft: spacing.md,
    flex: 1,
  },
  fileName: {
    ...typography.styles.body2,
    color: colors.text.primary,
    fontWeight: '600',
  },
  fileSize: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  removeButton: {
    padding: spacing.sm,
  },
  processButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.buttonPaddingVertical,
    borderRadius: spacing.cardRadius,
  },
  processButtonText: {
    ...typography.styles.button,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  instructionsList: {
    gap: spacing.lg,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primary[500],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
    marginTop: 2,
  },
  stepNumberText: {
    ...typography.styles.caption,
    color: colors.background.secondary,
    fontWeight: '600',
  },
  instructionText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    flex: 1,
    lineHeight: 20,
  },
});

export default BankStatementScreen;
